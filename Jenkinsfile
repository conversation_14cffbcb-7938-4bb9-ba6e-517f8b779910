pipeline {
    agent any

    parameters {
        string(
            name: 'JIRA_ISSUE_ID',
            defaultValue: 'SWS-34515',
            description: 'JIRA Issue ID (e.g., PROJ-123)',
            trim: true
        )
        string(
            name: 'JIRA_SERVER_URL',
            defaultValue: 'https://jira.skywindgroup.com',
            description: 'JIRA Server URL',
            trim: true
        )
    }

    environment {
        JIRA_CREDENTIALS_ID = 'CMBuilderSkywindCrenedtials'
        JIRA_ISSUE_ID_PATTERN = '^[A-Z]+-[0-9]+$'
    }

    stages {
        stage('Validate Parameters') {
            steps {
                script {
                    if (!params.JIRA_ISSUE_ID) {
                        error("JIRA_ISSUE_ID parameter is required. Please provide a valid JIRA issue ID (e.g., PROJ-123)")
                    }
                    if (!params.JIRA_ISSUE_ID.matches(env.JIRA_ISSUE_ID_PATTERN)) {
                        error("Invalid JIRA Issue ID format: '${params.JIRA_ISSUE_ID}'. Expected format: PROJECT-NUMBER (e.g., PROJ-123)")
                    }
                    if (!params.JIRA_SERVER_URL) {
                        error("JIRA_SERVER_URL parameter is required. Please provide a valid JIRA server URL")
                    }
                    echo "✓ JIRA Issue ID: ${params.JIRA_ISSUE_ID}"
                    echo "✓ JIRA Server URL: ${params.JIRA_SERVER_URL}"
                }
            }
        }

        stage('Connect to JIRA') {
            steps {
                withCredentials([usernameColonPassword(credentialsId: env.JIRA_CREDENTIALS_ID, variable: 'USERPASS')]) {
                    sh('./load.sh $USERPASS')
                }
            }
        }
    }

    post {
        always {
            echo "=== Pipeline Execution Summary ==="
            echo "JIRA Issue ID: ${params.JIRA_ISSUE_ID}"
            echo "JIRA Server: ${params.JIRA_SERVER_URL}"
            echo "Build Status: ${currentBuild.currentResult}"
            echo "Build Duration: ${currentBuild.durationString}"
        }

        success {
            echo "✓ Pipeline completed successfully"
            echo "JIRA issue '${params.JIRA_ISSUE_ID}' details retrieved and displayed"
        }

        failure {
            echo "✗ Pipeline failed"
            echo "Please check the logs above for error details"
        }

        cleanup {
            script {
                env.JIRA_ISSUE_DATA = null
            }
        }
    }
}