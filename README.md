# SW AI Agents - JIRA Integration

This project provides both a Jenkins pipeline and a CLI tool for JIRA integration, including posting comments to JIRA issues.

## Features

### CLI Tool
- **CLI Interface**: Command-line tool to post comments to JIRA issues
- **Environment Configuration**: Uses environment variables for secure credential management
- **Parameter Validation**: Validates JIRA issue ID format and required parameters
- **Comprehensive Error Handling**: Handles various failure scenarios with meaningful error messages

### Jenkins Pipeline
- **Secure Authentication**: Uses Jenkins credentials store for JIRA authentication
- **Detailed Issue Display**: Shows complete issue information in a formatted console output
- **Configurable JIRA Server**: Supports different JIRA server URLs

## CLI Tool Setup and Usage

### Environment Configuration

**REQUIRED**: Create a `.env` file in the project root with your JIRA configuration:

```bash
# Copy the example file
cp .env.example .env

# Edit the .env file with your credentials (REQUIRED)
JIRA_SERVER_URL=https://your-jira-server.com
JIRA_CREDENTIALS=username:password_or_api_token
```

**Note**: Both `JIRA_SERVER_URL` and `JIRA_CREDENTIALS` are now required. The application will fail to start without them.

### CLI Usage

```bash
# Using npm script
npm run prepare -- --issue-id SWS-34515
npm run prepare -- -i SWS-34515

# Using npx directly
npx tsx src/prepare.ts --issue-id SWS-34515
npx tsx src/prepare.ts -i SWS-34515

# Show help
npm run prepare -- --help
npx tsx src/prepare.ts --help
```

### CLI Options

- `-i, --issue-id <ID>`: JIRA issue ID (e.g., SWS-34515) - **Required**
- `-h, --help`: Show help message

### Environment Variables

You can also override configuration using environment variables:

```bash
# Override JIRA server URL
JIRA_SERVER_URL=https://custom-jira.com npm run prepare -- -i SWS-34515

# Override credentials
JIRA_CREDENTIALS=myuser:mytoken npm run prepare -- -i SWS-34515
```

## Programmatic Usage

You can also use the `JiraClient` class directly in your code:

```typescript
import { JiraClient } from './src/jira.js';

// JiraClient requires explicit configuration - no environment variables used internally
const client = new JiraClient({
  serverUrl: 'https://your-jira-server.com',
  credentials: 'username:password_or_api_token',
  commentMarker: '[BOT]' // Optional: custom marker (default: '[AI]')
});

// Validate issue ID
const isValid = client.validateJiraIssueId('SWS-34515');

// Post a comment (will update existing comment if found)
await client.comments('SWS-34515').post('Your comment here');

// Post a comment with a custom marker for identification
await client.comments('SWS-34515').post('Status update: In progress', 'Status update');

// Get all comments from an issue
const comments = await client.comments('SWS-34515').get();

// Update a specific comment by ID
await client.comments('SWS-34515').update('comment-id', 'Updated comment text');
```

### JiraClient Methods

- `validateJiraIssueId(issueId: string): boolean` - Validates JIRA issue ID format
- `getCurrentUser(): Promise<JiraUser | null>` - Gets current user information
- `comments(issueId: string): Comments` - Returns a Comments instance for the specified issue

### Comments Class Methods

- `get(): Promise<JiraCommentResponse[]>` - Gets all comments from the issue
- `post(comment: string): Promise<void>` - Posts a comment or updates an existing one (uses configured commentMarker)
- `update(commentId: string, comment: string): Promise<JiraCommentResponse>` - Updates a specific comment

#### Comment Update Behavior

The `postComment` method now intelligently handles existing comments:

1. **First, it looks for comments containing a specific marker** (default: "[AI]")
2. **If no marker-based comment is found, it looks for the most recent comment by the current user**
3. **If an existing comment is found, it updates that comment instead of creating a new one**
4. **If no existing comment is found, it creates a new comment**

This prevents comment spam and keeps the issue clean by reusing existing comments when appropriate.

### Configuration Options

The `JiraClient` constructor requires a configuration object:

```typescript
interface JiraConfig {
  serverUrl: string;      // JIRA server URL (required)
  credentials: string;    // username:password or username:api_token (required)
  commentMarker?: string; // Marker to identify AI comments (default: "[AI]")
}
```

**Note**: The `JiraClient` class does not use environment variables internally. All configuration must be passed explicitly to the constructor.

## Jenkins Pipeline Setup

### Prerequisites

### 1. Jenkins Setup
- Jenkins with Pipeline plugin installed
- Access to create and configure Jenkins jobs

### 2. JIRA Credentials
Create a Jenkins credential with the following details:
- **Type**: Username with password
- **ID**: `jira-api-credentials` (or update the `JIRA_CREDENTIALS_ID` in the pipeline)
- **Username**: Your JIRA username or email
- **Password**: Your JIRA password or API token (recommended)

### 3. JIRA API Token (Recommended)
For better security, use an API token instead of your password:
1. Go to your JIRA profile settings
2. Navigate to Security → API tokens
3. Create a new API token
4. Use this token as the password in Jenkins credentials

## Pipeline Parameters

### Required Parameters
- **JIRA_ISSUE_ID**: The JIRA issue key (e.g., "PROJ-123")
- **JIRA_SERVER_URL**: Your JIRA server URL (e.g., "https://your-company.atlassian.net")

## Usage

### 1. Create Jenkins Job
1. Create a new Pipeline job in Jenkins
2. In the Pipeline section, select "Pipeline script from SCM" or paste the Jenkinsfile content
3. Save the job configuration

### 2. Run the Pipeline
1. Click "Build with Parameters"
2. Enter the JIRA issue ID (e.g., "PROJ-123")
3. Enter your JIRA server URL (or use the default)
4. Click "Build"

### 3. View Results
The pipeline will display detailed issue information including:
- Issue key, summary, status, and priority
- Assignee, reporter, and creator information
- Creation and update dates
- Issue description
- Components, labels, fix versions (if available)
- Resolution and environment details (if available)

## Pipeline Stages

1. **Validate Parameters**: Validates input parameters and JIRA issue ID format
2. **Connect to JIRA**: Tests connectivity to the JIRA server
3. **Retrieve JIRA Issue**: Fetches the complete issue data from JIRA
4. **Display JIRA Issue Details**: Formats and displays the issue information

## Error Handling

The pipeline handles various error scenarios:
- Invalid JIRA issue ID format
- Missing required parameters
- JIRA server connectivity issues
- Authentication failures
- Issue not found (404)
- Access denied (403)
- General API errors

## Customization

### Changing Credentials ID
Update the `JIRA_CREDENTIALS_ID` environment variable in the Jenkinsfile:
```groovy
environment {
    JIRA_CREDENTIALS_ID = 'your-custom-credentials-id'
}
```

### Modifying Issue ID Pattern
Update the regex pattern for issue ID validation:
```groovy
environment {
    JIRA_ISSUE_ID_PATTERN = '^[A-Z]+-[0-9]+$'  // Current pattern
}
```

### Adding Custom Fields
To display additional JIRA fields, modify the "Display JIRA Issue Details" stage and add the desired fields from the `fields` object.

## Security Considerations

### CLI Tool Security
- **Required configuration**: Both `JIRA_SERVER_URL` and `JIRA_CREDENTIALS` are now required
- **Never commit `.env` files**: The `.env` file is already in `.gitignore`
- **Use API tokens**: Always use JIRA API tokens instead of passwords
- **Environment variables**: Use environment variables for production deployments
- **File permissions**: Ensure `.env` file has restricted permissions (`chmod 600 .env`)
- **No default credentials**: The application will not start without proper configuration

### Jenkins Pipeline Security
- Always use API tokens instead of passwords
- Store credentials securely in Jenkins credentials store
- Limit JIRA user permissions to read-only access
- Use HTTPS for JIRA server connections
- Regularly rotate API tokens

## Troubleshooting

### Common Issues

1. **Authentication Failed (401)**
   - Verify JIRA credentials in Jenkins
   - Check if API token is valid
   - Ensure username/email is correct

2. **Issue Not Found (404)**
   - Verify the issue ID exists in JIRA
   - Check if the issue is in the correct project
   - Ensure proper case sensitivity

3. **Access Denied (403)**
   - Verify user has permission to view the issue
   - Check project permissions in JIRA
   - Ensure user is not restricted

4. **Connection Failed**
   - Verify JIRA server URL is correct
   - Check network connectivity from Jenkins
   - Ensure JIRA server is accessible

## Example Output

```
=== JIRA Issue Details ===
┌─────────────────────────────────────────────────────────────────
│ JIRA ISSUE INFORMATION
├─────────────────────────────────────────────────────────────────
│ Issue Key:        PROJ-123
│ Summary:          Fix login authentication bug
│ Status:           In Progress
│ Priority:         High
│ Issue Type:       Bug
│ Project:          My Project (PROJ)
├─────────────────────────────────────────────────────────────────
│ PEOPLE
├─────────────────────────────────────────────────────────────────
│ Assignee:         John Doe
│ Reporter:         Jane Smith
│ Creator:          Jane Smith
├─────────────────────────────────────────────────────────────────
│ DATES
├─────────────────────────────────────────────────────────────────
│ Created:          2024-01-15T10:30:00.000+0000
│ Updated:          2024-01-16T14:20:00.000+0000
│ Due Date:         2024-01-20T23:59:59.000+0000
└─────────────────────────────────────────────────────────────────
```
