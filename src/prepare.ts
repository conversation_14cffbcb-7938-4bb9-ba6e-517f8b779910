import { parseArgs } from 'node:util';
import { config } from 'dotenv';
import { JiraClient } from './jira.js';
import { loadGitRepos } from './git.js';

// Load environment variables from .env file
config();

const PROJECTS: Record<string, string[]> = {
  UBO: [
    'ssh://******************************:7999/byswbo/sw-ubo-hub-base.git',
    'ssh://******************************:7999/byswbo/sw-ubo-hub-bridge.git',
    'ssh://******************************:7999/byswbo/sw-ubo-hub-casino.git',
    'ssh://******************************:7999/byswbo/sw-ubo-library-common.git'
  ]
};

async function run(): Promise<void> {
  const { values: { 'issue-id': issueId } } = parseArgs({
    args: process.argv.slice(2),
    options: {
      'issue-id': {
        type: 'string',
        short: 'i',
      }
    },
  });
  if (!issueId) {
    throw new Error('JIRA issue ID is required');
  }

  const jiraClient = new JiraClient({
    serverUrl: process.env.JIRA_SERVER_URL,
    credentials: process.env.JIRA_CREDENTIALS,
  });

  console.log(`Processing JIRA issue: ${issueId}`);

  // Step 1: Check if issue belongs to a known project and load git repos
  const key = issueId.match(/^([A-Z]+)-[0-9]+$/)?.[1];
  if (key) {
    const repos = PROJECTS[key];
    if (repos) {
      await loadGitRepos(repos);
    } else {
      throw new Error(`Project '${key}' not found in PROJECTS configuration`);
    }
  } else {
    throw new Error(`Could not extract project key from issue ID: ${issueId}`);
  }

  // Step 2: Create initial tracking comment
  await jiraClient.comments(issueId).post('is working…');
}

run().catch((error) => {
  console.error(error.message);
  process.exit(1);
});
