import fetch, { type RequestInit } from 'node-fetch';

export interface JiraCommentResponse {
  id: string;
  body: string;
  created: string;
  updated?: string;
  author?: {
    name: string;
    displayName: string;
    emailAddress?: string;
  };
  updateAuthor?: {
    name: string;
    displayName: string;
    emailAddress?: string;
  };
}

export interface JiraCommentsResponse {
  comments: JiraCommentResponse[];
  maxResults: number;
  total: number;
  startAt: number;
}

export interface JiraConfig {
  serverUrl?: string;
  credentials?: string;
  commentMarker?: string;
}

export interface JiraUser {
  name: string;
  displayName: string;
  emailAddress?: string;
}

export class JiraClient {
  private readonly serverUrl: string;
  private readonly headers: Record<string, string>;
  readonly commentMarker: string;

  constructor({ serverUrl, credentials, commentMarker }: JiraConfig) {
    if (!serverUrl) {
      throw new Error('serverUrl is required in config.');
    }
    if (!credentials) {
      throw new Error('credentials is required in config.');
    }
    this.serverUrl = serverUrl.endsWith('/') ? serverUrl.substring(0, serverUrl.length - 1) : serverUrl;
    this.commentMarker = commentMarker || '[AI]';
    this.headers = {
      'Authorization': `Basic ${Buffer.from(credentials).toString('base64')}`,
      'Accept': 'application/json',
    };
  }

  private async got<T>(uri: string, { headers, json, ...options }: RequestInit & { json?: Record<string, any> } = {}) {
    const response = await fetch(`${this.serverUrl}${uri.startsWith('/') ? uri : `/${uri}`}`, {
      ...options,
      headers: {
        ...(json ? { 'Content-Type': 'application/json' } : {}),
        ...(headers || {}),
        ...this.headers,
      },
      ...(json ? { body: JSON.stringify(json) } : {}),
    });
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    return response.json() as Promise<T>;
  }

  comments(issueId: string) {
    return new this.Comments(issueId, this);
  }

  Comments = class {
    private readonly url: string;
    private readonly client: JiraClient;
    private readonly marker: string;

    constructor(issueId: string, client: JiraClient) {
      this.url = `/rest/api/2/issue/${issueId}/comment`;
      this.client = client;
      this.marker = client.commentMarker;
    }

    async get(): Promise<JiraCommentResponse[]> {
      const result = await this.client.got<JiraCommentsResponse>(this.url, {
        method: 'GET',
      });
      return result.comments || [];
    }

    private async create(body: string): Promise<JiraCommentResponse> {
      return this.client.got<JiraCommentResponse>(this.url, {
        method: 'POST',
        json: { body },
      });
    }

    async update(commentId: string, body: string): Promise<JiraCommentResponse> {
      return this.client.got<JiraCommentResponse>(`${this.url}/${commentId}`, {
        method: 'PUT',
        json: { body },
      });
    }

    async post(body: string): Promise<void> {
      const comment = `${this.marker} ${body}`.trim();
      const comments = await this.get();
      if (comments.length > 0) {
        const currentUser = await this.client.getCurrentUser();
        const existingComment = comments
          .filter(c => c.author?.name === currentUser?.name || c.author?.emailAddress === currentUser?.emailAddress)
          .find(c => c.body?.trim().includes(this.marker));
        if (existingComment) {
          await this.update(existingComment.id, comment);
          return;
        }
      }
      await this.create(comment);
    }
  };

  async getCurrentUser() {
    try {
      return await this.got<JiraUser>('/rest/api/2/myself', {
        method: 'GET',
      });
    } catch (error) {
      console.warn('Could not get current user information:', error);
      return null;
    }
  }
}
